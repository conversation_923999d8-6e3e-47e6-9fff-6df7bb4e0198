const { ServiceBroker } = require("moleculer");
const http = require("http");
const url = require("url");
const fs = require("fs");
const path = require("path");

// 创建一个简单的Steedos服务器
const broker = new ServiceBroker({
    namespace: "steedos",
    nodeID: "simple-server",
    transporter: "redis://127.0.0.1:6379",
    cacher: "redis://127.0.0.1:6379/1",
    logger: true,
    logLevel: "info"
});

// 创建一个简单的metadata-server服务
broker.createService({
    name: "metadata-server",
    actions: {
        getMetadata: {
            handler(ctx) {
                return {
                    version: "1.0.0",
                    status: "running"
                };
            }
        }
    },
    started() {
        this.logger.info("Metadata server started");
    }
});

// 创建一个简单的project服务
broker.createService({
    name: "@steedos/service-project",
    actions: {
        getProjects: {
            handler(ctx) {
                return [];
            }
        }
    },
    started() {
        this.logger.info("Project service started");
    }
});

// 创建一个简单的packages服务
broker.createService({
    name: "@steedos/service-packages",
    actions: {
        getPackages: {
            handler(ctx) {
                return [];
            }
        }
    },
    started() {
        this.logger.info("Packages service started");
    }
});

// 创建一个简单的objectql服务
broker.createService({
    name: "objectql",
    actions: {
        query: {
            handler(ctx) {
                return { data: [] };
            }
        }
    },
    started() {
        this.logger.info("ObjectQL service started");
    }
});

// 创建一个简单的data-import服务
broker.createService({
    name: "@steedos/data-import",
    actions: {
        import: {
            handler(ctx) {
                return { success: true };
            }
        }
    },
    started() {
        this.logger.info("Data import service started");
    }
});

// 创建用户认证服务
broker.createService({
    name: "users",
    actions: {
        login: {
            params: {
                username: "string",
                password: "string"
            },
            handler(ctx) {
                const { username, password } = ctx.params;

                // 简单的用户验证（实际项目中应该使用数据库和加密）
                if (username === "admin" && password === "admin") {
                    return {
                        success: true,
                        user: {
                            id: "admin",
                            username: "admin",
                            name: "管理员",
                            email: "<EMAIL>"
                        },
                        token: "simple-token-" + Date.now()
                    };
                } else {
                    throw new Error("用户名或密码错误");
                }
            }
        },
        verify: {
            params: {
                token: "string"
            },
            handler(ctx) {
                const { token } = ctx.params;
                if (token && token.startsWith("simple-token-")) {
                    return {
                        valid: true,
                        user: {
                            id: "admin",
                            username: "admin",
                            name: "管理员",
                            email: "<EMAIL>"
                        }
                    };
                }
                return { valid: false };
            }
        }
    },
    started() {
        this.logger.info("Users service started");
    }
});

// 创建Web服务器
const PORT = process.env.PORT || 5000;

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (path === '/') {
        // 重定向到登录页面
        res.writeHead(302, { 'Location': '/login' });
        res.end();
    } else if (path === '/login') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <html>
                <head>
                    <title>Steedos Platform - 登录</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 0;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            min-height: 100vh;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .login-container {
                            background: white;
                            padding: 40px;
                            border-radius: 10px;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                            width: 100%;
                            max-width: 400px;
                        }
                        .logo {
                            text-align: center;
                            margin-bottom: 30px;
                        }
                        .logo h1 {
                            color: #333;
                            margin: 0;
                            font-size: 28px;
                        }
                        .form-group {
                            margin-bottom: 20px;
                        }
                        label {
                            display: block;
                            margin-bottom: 5px;
                            color: #333;
                            font-weight: bold;
                        }
                        input[type="text"], input[type="password"] {
                            width: 100%;
                            padding: 12px;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            font-size: 16px;
                            box-sizing: border-box;
                        }
                        .login-btn {
                            width: 100%;
                            padding: 12px;
                            background: #667eea;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            font-size: 16px;
                            cursor: pointer;
                            transition: background 0.3s;
                        }
                        .login-btn:hover {
                            background: #5a6fd8;
                        }
                        .demo-info {
                            margin-top: 20px;
                            padding: 15px;
                            background: #f8f9fa;
                            border-radius: 5px;
                            font-size: 14px;
                            color: #666;
                        }
                        .error {
                            color: #e74c3c;
                            margin-top: 10px;
                            display: none;
                        }
                    </style>
                </head>
                <body>
                    <div class="login-container">
                        <div class="logo">
                            <h1>🚀 Steedos</h1>
                            <p>低代码平台</p>
                        </div>
                        <form id="loginForm">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" id="username" name="username" value="admin" required>
                            </div>
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" id="password" name="password" value="admin" required>
                            </div>
                            <button type="submit" class="login-btn">登录</button>
                            <div id="error" class="error"></div>
                        </form>
                        <div class="demo-info">
                            <strong>演示账户：</strong><br>
                            用户名: admin<br>
                            密码: admin
                        </div>
                    </div>
                    <script>
                        document.getElementById('loginForm').addEventListener('submit', async function(e) {
                            e.preventDefault();

                            const username = document.getElementById('username').value;
                            const password = document.getElementById('password').value;
                            const errorDiv = document.getElementById('error');

                            try {
                                const response = await fetch('/api/login', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ username, password })
                                });

                                const result = await response.json();

                                if (result.success) {
                                    localStorage.setItem('steedos_token', result.token);
                                    localStorage.setItem('steedos_user', JSON.stringify(result.user));
                                    window.location.href = '/dashboard';
                                } else {
                                    errorDiv.textContent = result.error || '登录失败';
                                    errorDiv.style.display = 'block';
                                }
                            } catch (error) {
                                errorDiv.textContent = '网络错误，请重试';
                                errorDiv.style.display = 'block';
                            }
                        });
                    </script>
                </body>
            </html>
        `);
    } else if (path === '/dashboard') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <html>
                <head>
                    <title>Steedos Platform - 仪表板</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 0;
                            background: #f5f5f5;
                        }
                        .header {
                            background: #667eea;
                            color: white;
                            padding: 15px 20px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .header h1 {
                            margin: 0;
                            font-size: 24px;
                        }
                        .user-info {
                            display: flex;
                            align-items: center;
                            gap: 15px;
                        }
                        .logout-btn {
                            background: rgba(255,255,255,0.2);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                        }
                        .container {
                            max-width: 1200px;
                            margin: 20px auto;
                            padding: 0 20px;
                        }
                        .dashboard-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 20px;
                            margin-top: 20px;
                        }
                        .card {
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .card h3 {
                            margin-top: 0;
                            color: #333;
                        }
                        .status-indicator {
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            margin-right: 8px;
                        }
                        .status-online { background: #27ae60; }
                        .status-offline { background: #e74c3c; }
                        .service-list {
                            list-style: none;
                            padding: 0;
                        }
                        .service-list li {
                            padding: 8px 0;
                            border-bottom: 1px solid #eee;
                            display: flex;
                            align-items: center;
                        }
                        .quick-actions {
                            display: flex;
                            gap: 10px;
                            flex-wrap: wrap;
                        }
                        .action-btn {
                            background: #667eea;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            text-decoration: none;
                            display: inline-block;
                        }
                        .action-btn:hover {
                            background: #5a6fd8;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🚀 Steedos Platform</h1>
                        <div class="user-info">
                            <span id="userName">加载中...</span>
                            <button class="logout-btn" onclick="logout()">退出</button>
                        </div>
                    </div>
                    <div class="container">
                        <div class="dashboard-grid">
                            <div class="card">
                                <h3>系统状态</h3>
                                <p><span class="status-indicator status-online"></span>系统运行正常</p>
                                <p><strong>运行时间:</strong> <span id="uptime">计算中...</span></p>
                                <p><strong>端口:</strong> ${PORT}</p>
                                <p><strong>环境:</strong> 开发环境</p>
                            </div>
                            <div class="card">
                                <h3>服务状态</h3>
                                <ul class="service-list" id="serviceList">
                                    <li>加载中...</li>
                                </ul>
                            </div>
                            <div class="card">
                                <h3>快速操作</h3>
                                <div class="quick-actions">
                                    <a href="/api/health" class="action-btn" target="_blank">健康检查</a>
                                    <a href="/api/services" class="action-btn" target="_blank">服务详情</a>
                                    <button class="action-btn" onclick="refreshData()">刷新数据</button>
                                </div>
                            </div>
                            <div class="card">
                                <h3>系统信息</h3>
                                <p><strong>平台版本:</strong> 社区版 v1.0</p>
                                <p><strong>数据库:</strong> MongoDB</p>
                                <p><strong>缓存:</strong> Redis</p>
                                <p><strong>消息队列:</strong> Redis</p>
                            </div>
                        </div>
                    </div>
                    <script>
                        const startTime = Date.now();

                        // 检查登录状态
                        function checkAuth() {
                            const token = localStorage.getItem('steedos_token');
                            const user = localStorage.getItem('steedos_user');

                            if (!token || !user) {
                                window.location.href = '/login';
                                return false;
                            }

                            const userData = JSON.parse(user);
                            document.getElementById('userName').textContent = userData.name || userData.username;
                            return true;
                        }

                        // 退出登录
                        function logout() {
                            localStorage.removeItem('steedos_token');
                            localStorage.removeItem('steedos_user');
                            window.location.href = '/login';
                        }

                        // 更新运行时间
                        function updateUptime() {
                            const uptime = Math.floor((Date.now() - startTime) / 1000);
                            const hours = Math.floor(uptime / 3600);
                            const minutes = Math.floor((uptime % 3600) / 60);
                            const seconds = uptime % 60;
                            document.getElementById('uptime').textContent =
                                hours + '小时 ' + minutes + '分钟 ' + seconds + '秒';
                        }

                        // 加载服务状态
                        async function loadServices() {
                            try {
                                const response = await fetch('/api/services');
                                const data = await response.json();
                                const serviceList = document.getElementById('serviceList');

                                serviceList.innerHTML = '';
                                data.services.forEach(service => {
                                    const li = document.createElement('li');
                                    li.innerHTML =
                                        '<span class="status-indicator status-online"></span>' +
                                        service.name;
                                    serviceList.appendChild(li);
                                });
                            } catch (error) {
                                console.error('加载服务失败:', error);
                            }
                        }

                        // 刷新数据
                        function refreshData() {
                            loadServices();
                            updateUptime();
                        }

                        // 初始化
                        if (checkAuth()) {
                            loadServices();
                            setInterval(updateUptime, 1000);
                            setInterval(loadServices, 30000); // 每30秒刷新服务状态
                        }
                    </script>
                </body>
            </html>
        `);
    } else if (path === '/api/login') {
        if (req.method === 'POST') {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', async () => {
                try {
                    const { username, password } = JSON.parse(body);
                    const result = await broker.call('users.login', { username, password });
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify(result));
                } catch (error) {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: error.message }));
                }
            });
        } else {
            res.writeHead(405, { 'Content-Type': 'text/plain' });
            res.end('Method Not Allowed');
        }
    } else if (path === '/api/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            timestamp: new Date().toISOString(),
            services: broker.registry.getServiceList({ withActions: false })
        }));
    } else if (path === '/api/services') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            services: broker.registry.getServiceList({ withActions: true })
        }));
    } else {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('Not Found');
    }
});

// 启动服务
async function start() {
    try {
        // 启动Moleculer broker
        await broker.start();
        console.log("✅ Moleculer broker started");
        
        // 启动Web服务器
        server.listen(PORT, () => {
            console.log(`✅ Web server started on http://localhost:${PORT}`);
            console.log(`🎉 Steedos Platform is running!`);
        });
    } catch (error) {
        console.error("❌ Failed to start server:", error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down gracefully...');
    await broker.stop();
    process.exit(0);
});

start();
