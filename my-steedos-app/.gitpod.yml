image: steedos/gitpod-workspace-base:2.2.5

# List the ports you want to expose and what to do when they are served. See https://www.gitpod.io/docs/config-ports/
ports:
  - port: 5000
    onOpen: ignore
    visibility: public
  - port: 6379
    onOpen: ignore
  - port: 27017
    onOpen: ignore
    visibility: public
    
# List the start up tasks. You can start them in parallel in multiple terminals. See https://www.gitpod.io/docs/config-start-tasks/
tasks:
  - name: Steedos Platform
    command: |
      cd steedos-platform
      echo "ROOT_URL=$(gp url 5000)" >> .env.local
      docker-compose up
  - name: Steedos Packages
    command: |
      echo "METADATA_SERVER=$(gp url 5000)" >> .env.local
      yarn
      gp await-port 5000
      yarn start
  - name: Node-RED
    command: |
      gp await-port 6379
      cd nodered-app
      npm i --no-optional 
      npm start

vscode:
  extensions:
    - redhat.vscode-yaml
    - steedos.steedosdx-vscode
    - steedos.steedosdx-vscode-core
    - ms-ceintl.vscode-language-pack-zh-hans
