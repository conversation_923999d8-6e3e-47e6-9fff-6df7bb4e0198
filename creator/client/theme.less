//@import '{}/node_modules/@steedos/builder-object/dist/builder-object.react.css';
// body{
//     &.steedos.fixed{
//         position: unset!important;
//     }
// }
input::-webkit-contacts-auto-fill-button {
    // 隐藏safari浏览器中新建记录时文本框会出现的自动填充人按钮
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    position: absolute;
    right: 0;
}

.steedos.creator .creator-content-wrapper .content-wrapper {
    top: 0;
}

.steedos.creator .creator-content-wrapper .main-sidebar {
    margin-top: 0;
}

.steedos.workflow {
    .creator-list-wrapper {
        top: 50px;
    }
    
}
.steedos.creator .creator-content-wrapper{
    .app-object-home, .app-home{
        height: 100%;
        width: 100%;
    }
}
.steedos{
    .modal-dialog{
        .modal-body{
            // summernote包的图片上传标签样式优化
            .form-group.note-group-select-from-files{
                label:after {
                    content: "选择文件";
                    position: absolute;
                    margin-top: 15px;
                    left: 15px;
                    top: 30px;
                    padding: 3px;
                    border-radius: 4px;
                    padding-left: 1rem;
                    padding-right: 1rem;
                    text-align: center;
                    border: 1px solid #dddbda;
                    color: #0070d2;
                    font-weight: 420;
                    cursor: pointer;
                    width: 88px;
                }
                .note-image-input.form-control{
                    opacity: 0;
                    width: 88px;
                }
            }
        }
    }
    .dropdown-menu{
        // 参考saleforce这块是用的12px
        font-size: 14px;
        min-width: 172px;
    }

    .creator-mobile-header{
        .steedos-header-favorites, .header-notifications{
            section.slds-popover {
                position: fixed !important;
                top: 44px !important;
                left: 10px !important;
                right: 10px !important;
                bottom: auto;
                width: auto;
                left: 0 !important;
                right: 0 !important;
                border-top: none;
                border-left: none;
                border-right: none;
                border-radius: 0;
                &:after,&:before{
                    visibility: hidden;
                }
            }
        }
    }

    #chooseFlowTree-myFavorite__label{
        a{
            svg{
                display: inline;
            }
        }
    }
    .slds-section{
        .slds-section__title{
            /*因为我们的steedos_button这个template外面一定会带一层div，造成section下的折叠控件样式异常，需要下面的代码修正*/
            .steedos-button-wrap{
                width: 100%;
                display: flex;
                height: 32px;
                >.slds-section__title-action{
                    justify-content: flex-start;
                }
            }
        }
    }
}

.slds-global-header .slds-global-header__item.slds-global-header__item--search{
    display: none;
}