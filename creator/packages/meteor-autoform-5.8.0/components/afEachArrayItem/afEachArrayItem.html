<template name="afEachArrayItem">
  {{! This is a block component and doesn't render anything visible, so no customizable template is needed for this}}
  {{#with innerContext}}
    {{#each this}}
    {{#if this.removed}}
    <input type="hidden" name="{{this.name}}" data-schema-key="{{this.name}}" data-null-value="true" value="">
    {{else}}
    {{> Template.contentBlock this}}
    {{/if}}
    {{/each}}
  {{/with}}
</template>
