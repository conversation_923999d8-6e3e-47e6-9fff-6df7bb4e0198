
.cf_organization_modal{
    z-index:6666;
    .box {
        border-radius: 0px;
    }

    .breadcrumb{
        float: right;
        background: transparent;
        margin-top: 0;
        margin-bottom: 0;
        font-size: 12px;
        padding: 7px 5px;
        top: 15px;
        right: 10px;
        border-radius: 2px;
    }

    .breadcrumb >li+li:before{
        content: '>\00a0'
    }

    .breadcrumb >li>a{
        font-size: 12px;
        color: #444;
        text-decoration: none;
        display: inline-block;
    }

    .box-header{
        border-bottom: 0;
        padding: 0;
        padding-left: 10px;
    }

    label {
        cursor: pointer;
    }

    .organizations_tree{
        color: #444;
        font-size: 14px;
        font-weight:400;
    }

    .modal-header{
        .navigation-bar{
            background-color: #fff;
            padding: 0px;
            // height: 42px;
            border-bottom: 0px;

            .cf-contact-modal-menu{
                position: fixed;
                top: inherit;
                width: 100%;
                border: 0px;
            }

            .box-body{
                overflow-y: auto;
                max-height: 340px;
                min-height: 340px;
            }

            .box-solid{
                border: 0px;
            }
        }
    }

    .contacts-modal-body{
        padding-bottom: 0px;
        overflow-y:auto;
    }
    

    .cf_space_user_list {
        .box-body{
            padding-top: 0px;
            min-height: 466px;
        }

        .cf_space_user_list_table{

            th{
                padding-right: 0;
            }

            .user-name{
                img{
                    max-width: 27px;
                    margin-right: 5px;
                }

                font{
                    color: #444;
                    font-size: 14px;
                    font-weight:400;
                }
            }

            .for-input{
                width: 100%;
                margin-bottom: 0px;
            }
        }
    }

    .cf_organization{
        .box-body{
            // overflow-y: auto;
            // overflow-x: auto;
            // max-height: 466px;
            // min-height: 466px;
        }
    }

    #selectTagModal-content {
        overflow-y: auto;
        max-height: 500px; 
    }

    .selectTag-users > tbody > tr > td{
        padding: 4px 6px 4px 6px;
    }

    .modal-footer{
        text-align: left;
    }

    .selectTagButton{
        text-align: right;
        padding-top: 5px;
    }

    .valueLabel {
        background-color: #fff;
        border: 1px solid #ccc;
        display: inline-block;
        padding: 4px 6px;
        color: #555;
        vertical-align: middle;
        border-radius: 4px;
        width: 100%;
        min-height: 32px;
        line-height: 22px;
        cursor: text;
        list-style:none;
        overflow-x: auto;
    }

    .valueLabel:after{
        clear: both;
        content: '';
        display:block;
    }

    .valueLabel li {
        cursor: move;
        margin: 4px 5px 4px 0;
        //padding: 2px 4px;
        background-color: #1b9dec;
        text-align:center;
        color:#fff;
        font-size:14px;
        display: inline-block;
        span{
            display: inline-block;
            padding-left: 4px;
            padding-right: 4px;
        }
        a:hover{
            background-color: rgba(0, 0, 0, 0.05);
            //border-left: 1px solid #ffffff;
        }
    }

    .valueLabel .value-label-remove{
        background-color: #1b9dec;
        //border-left: 1px solid #5F9EDF;
        display: inline-block;
        color: #fff;
        cursor: pointer;
        padding-left: 4px;
        padding-right: 4px;
        padding-top: 2px;
        padding-bottom: 2px;
    }

    .selectTag-users .checkbox, .selectTag-users .radio{
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle;
    }

    .selectTag-users .checkbox > label, .selectTag-users .radio > label{
        padding-left: 0;
    }

    .selectTag-users .fa{
        margin-right: 5px;
    }

    .selectTag-users a{
        border-radius: 0;
        border-top: 0;
        border-left: 3px solid transparent;
        color: #444;
        font-size: 14px;
    }


    .selectTag-users img{
        max-width: 27px;
        margin-right: 5px;
    }

    .user label, .org label{
        font-weight: normal;
    }

    .selectUser.form-control[readonly], .selectOrg.form-control[readonly]{
        background-color: transparent;
        opacity: 1;
    }

    .selectUser.form-control[disabled], .selectOrg.form-control[disabled]{
        background-color: #eee;
        opacity: 1;
    }
}

.cf_organization_modal {
    .contacts-modal-body {
        padding: 0;
    }
    .box.box-solid.box-default {
        border: none;
        box-shadow: none;
        .box-body {
            padding: 0;
        }
    }
}