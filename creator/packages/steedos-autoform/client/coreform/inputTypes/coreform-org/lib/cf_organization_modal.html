<template name="cf_organization_modal">
    <div class="steedos modal fade cf_organization_modal" id="cf_organization_modal" tabindex="-1" role="dialog"
         aria-labelledby="myModalLabel" style='position: fixed;'>
        <div class="modal-dialog {{modalStyle showOrg}}" role="document">
            <div class="modal-content ins-modal">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{{title}}</h4>
                </div>
                <div class="modal-body contacts-modal-body modal-zoom">
                    <div class="row">
                        <div class="col-md-12">
                            {{> cf_organization data}}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="row" style="{{footer_display multiple}}">
                        <div class="col-md-9 col-sm-8 col-xs-8" id="valueLabel_div">
                            <ui id="valueLabel_ui" class="valueLabel">
                                <div id="valueLabel"></div>
                            </ui>
                        </div>
                        <div class="col-md-3 col-sm-4 col-xs-4 pull-right">
                            <button type="button" class="btn btn-primary" id='confirm' title='{{_ "OK"}}'
                                    style="width:100%">{{_ "OK"}}</button>
                        </div>
                    </div>
                    {{#if multiple}}

                    {{else}}
                        <div class="row">
                            <div class="col-md-3 pull-left" style="">
                                <button type="button" class="btn btn-default" id='remove' title='{{_ "remove"}}'
                                        style="width:100%">{{_ "remove"}}</button>
                            </div>
                            <div class="col-md-3 pull-right" style="">
                                <button type="button" class="btn btn-default" data-dismiss="modal" aria-label="Close" title='{{_ "close"}}'
                                        style="width:100%">{{_ "close"}}</button>
                            </div>
                        </div>
                    {{/if}}
                </div>

            </div>
        </div>
    </div>
</template>