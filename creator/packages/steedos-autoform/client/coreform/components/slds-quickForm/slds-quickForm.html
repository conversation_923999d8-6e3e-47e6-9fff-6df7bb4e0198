<template name="quickForm_slds">
	<div class="quickForm_slds {{#if horizontal}}horizontal{{/if}}">
	{{#autoForm qfAutoFormContext}}
		{{#if schemaFields.grouplessFields}}
			{{#each schemaFields.grouplessFields}}
				<div class="slds-grid view-page-section-row {{#if has_wide_field this}}wide-fields{{/if}}">
					{{#each this}}
						<div class="slds-has-flexi-truncate slds-p-horizontal_x-small full view-page-block-item">
							<div class="slds-form-element slds-form-element_edit slds-grow slds-hint-parent slds-p-vertical_xx-small override--slds-form-element uiInput {{#if hasInlineHelpText this}}has-inline-text{{/if}}">
								{{#if is_range this}}
									{{> range_field}}
								{{else}}
									{{#unless is_renge_end this}}
										{{#if this}}
											{{#if hasInlineHelpText this}}
												<div class="info-popover" id="info_popover_{{this}}" style="display: none">{{hasInlineHelpText this}}</div>
											{{/if}}
											{{#if horizontal}}
												{{> afQuickField name=this class=(getClass this) template="bootstrap3-horizontal" label-class="col-sm-3 col-xs-12" input-col-class="col-sm-9 col-xs-12"}}
											{{else}}
												{{> afQuickField name=this class=(getClass this)}}
											{{/if}}
										{{/if}}
									{{/unless}}
								{{/if}}
							</div>
						</div>
					{{/each}}
				</div>
			{{/each}}
		{{/if}}

		{{#if schemaFields.groupFields}}
			{{#each schemaFields.groupFields}}
				{{#if this.fields}}
					<div class="slds-section group-section {{#if autoExpandGroup}}slds-is-open{{/if}}">
						<h3 class="slds-section__title group-section-control">
							{{> steedos_button className="slds-section__title-action" iconClassName="slds-section__title-action-icon slds-button__icon_left" 
							iconCategory="utility" iconName="switch" variant="icon" label=this.name}}
						</h3>
						<div aria-hidden="false" class="slds-section__content" id="expando-unique-id">
							{{#each this.fields}}
								<div class="slds-grid view-page-section-row {{#if has_wide_field this}}wide-fields{{/if}}">
									{{#each this}}
									<div class="slds-has-flexi-truncate slds-p-horizontal_x-small full view-page-block-item">
										<div class="slds-form-element slds-form-element_edit slds-grow slds-hint-parent slds-p-vertical_xx-small override--slds-form-element uiInput {{#if hasInlineHelpText this}}has-inline-text{{/if}}">
										{{#if is_range this}}
											{{> range_field}}
										{{else}}
											{{#unless is_renge_end this}}
												{{#if this}}
													{{#if hasInlineHelpText this}}
														<div class="info-popover" id="info_popover_{{this}}" style="display: none">{{hasInlineHelpText this}}</div>
													{{/if}}
													{{#if horizontal}}
														{{> afQuickField name=this class=(getClass this) template="bootstrap3-horizontal" label-class="col-sm-3 col-xs-12" input-col-class="col-sm-9 col-xs-12"}}
													{{else}}
														{{> afQuickField name=this class=(getClass this)}}
													{{/if}}
												{{/if}}
											{{/unless}}
										{{/if}}
										</div>
									</div>
									{{/each}}
								</div>
							{{/each}}
						</div>
					</div>
				{{/if}}
			{{/each}}
		{{/if}}

		{{#if schemaFields.hiddenFields}}
			<div class="slds-grid view-page-section-row hidden-fields">
				<div class="slds-has-flexi-truncate slds-p-horizontal_x-small full view-page-block-item">
					{{#each schemaFields.hiddenFields}}
						<div class="slds-form-element slds-form-element_edit slds-grow slds-hint-parent slds-p-vertical_xx-small override--slds-form-element uiInput">
							{{#if this}}
								{{#if horizontal}}
									{{> afQuickField name=this class=(getClass this) template="bootstrap3-horizontal" label-class="col-sm-3 col-xs-12" input-col-class="col-sm-9 col-xs-12"}}
								{{else}}
									{{> afQuickField name=this class=(getClass this)}}
								{{/if}}
							{{/if}}
						</div>
					{{/each}}
				</div>
			</div>
		{{/if}}

		{{#if schemaFields.disabledFields}}
			<div class="slds-grid view-page-section-row disabled-fields">
				<div class="slds-has-flexi-truncate slds-p-horizontal_x-small full view-page-block-item">
					{{#each schemaFields.disabledFields}}
						<div class="slds-form-element slds-form-element_edit slds-grow slds-hint-parent slds-p-vertical_xx-small override--slds-form-element uiInput">
							{{#if this}}
								{{#if horizontal}}
									{{> afQuickField name=this disabled=false readonly=true class=(getClass this) template="bootstrap3-horizontal" label-class="col-sm-3 col-xs-12" input-col-class="col-sm-9 col-xs-12"}}
								{{else}}
									{{> afQuickField name=this disabled=false readonly=true class=(getClass this)}}
								{{/if}}
							{{/if}}
						</div>
					{{/each}}
				</div>
			</div>
		{{/if}}
	{{/autoForm}}
	</div>
</template>


<template name="range_field">
	<div class="range-field field-code-{{startName}}">
		<div class="form-group">
			<label class="col-sm-3 control-label" for="dao2yaGvD4SFjifBi">
				{{afFieldLabelText name=startName class="col-sm-3 col-xs-12"}}
			</label>
			<div class="col-sm-9 col-xs-12">
				<div class="row show-grid">
					<div class="col-sm-6 col-xs-6 start">{{> afQuickField name=startName class=(getClass this) label=false}}</div>
					<div class="col-sm-6 col-xs-6 end">{{> afQuickField name=endName class=(getClass this) label=false}}</div>
				</div>
			</div>
		</div>
	</div>
</template>