# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.
coffeescript@1.12.7_3

blaze-html-templates
check@1.3.0
ddp-rate-limiter@1.0.7
ddp-common@1.4.0
dynamic-import@0.3.0
ecmascript@0.14.2
ejson@1.1.0
email@1.2.3
underscore
http@1.4.0
jquery@1.11.10
logging@1.1.19
meteor-base@1.3.0
mobile-experience@1.0.5
mongo@1.4.2
steedos:meteor-fix
random@1.1.0
rate-limit@1.0.8
reactive-dict@1.2.0
reactive-var@1.0.11
reload@1.2.0
service-configuration@1.0.11
session@1.1.7
shell-server@0.3.1
spacebars
spacebars-compiler@1.1.3
standard-minifier-css@1.5.2
standard-minifier-js@2.3.1
tracker@1.1.3

less

accounts-ui
accounts-password

tmeasday:check-npm-versions
fourseven:scss
# fortawesome:fontawesome
tap:i18n

raix:push
aldeed:tabular
aldeed:collection2
aldeed:simple-schema
aldeed:autoform
steedos:autoform-bs-datetimepicker
steedos:creator-autoform-modals
steedos:autoform-bs-minicolors
steedos:autoform-file
steedos:autoform-lookup
steedos:autoform-tags
# perak:markdown
# q42:autoform-markdown
# steedos:markdown@0.0.1
steedos:autoform-dx-date-box

vazco:universe-autoform-select

# summernote:summernote@0.8.1
# mpowaga:autoform-summernote@0.4.4

# steedos:devexpress

gwendall:simple-schema-i18n

# francocatena:status

steedos:toastr@2.1.3

kadira:flow-router
kadira:blaze-layout
dburles:collection-helpers

momentjs:moment
mrt:moment
mrt:moment-timezone

steedos:loaders-css

steedos:smsqueue

steedos:ui

steedos:adminlte@2.3.12_2
steedos:i18n
steedos:base
steedos:theme
steedos:jstree
rubaxa:sortable
steedos:datatables-extensions

matb33:collection-hooks

steedos:cfs-steedos-cloud
steedos:cfs-aliyun
steedos:cfs-filesystem
steedos:cfs-s3
steedos:cfs-standard-packages
steedos:cfs-ui

steedos:e164-phones-countries
steedos:i18n-iso-countries
steedos:qcloud-smsqueue

smoral:sweetalert@1.1.1

peppelg:bootstrap-3-modal@1.0.4

# keepnox:perfect-scrollbar

# steedos:ionicons@0.1.7


# steedos:lightning-design-system


steedos:odata

steedos:logger@0.0.2

steedos:object-database


# steedos:weixin
# steedos:pay

# steedos:weixin-template-message-queue
# steedos:mini-web


steedos:mailqueue



# 档案系统相关
# steedos:qhd-archive-sync
# steedos:qhd-archive-xml

# steedos:vip-card
# steedos:vip-post
steedos:objects
steedos:objects-core
# steedos:objects-billing

steedos:webhookqueue

steedos:creator

steedos:instance-record-queue
# steedos:love
# steedos:app-chat

# steedos:app-tableau
# steedos:app-portal
# steedos:app-mailbase


# steedos:version
steedos:formbuilder
steedos:app-workflow

# steedos:objects-to-yaml

## 应用 apps
# steedos:app-informations
# ssteedos:app-cms
# steedos:app-example
# steedos:cms

# steedos:app-crm
# steedos:app-meeting
# steedos:app-projects
# steedos:qhd-archive

# steedos:oauth2-messenger

steedos:object-webhooks-queue

steedos:workflow
steedos:workflow-chart
steedos:api
keepnox:perfect-scrollbar
jeremy:selectize
comerc:autoform-selectize
steedos:slipjs
react-template-helper

steedos:webkit-notification

steedos:huaweipush

steedos:api-authenticate-user
meteorhacks:subs-manager
lamhieu:unblock

steedos:records-qhd