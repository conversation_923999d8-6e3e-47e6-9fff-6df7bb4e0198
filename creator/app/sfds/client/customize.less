
// 修正记录显示页面，label宽度过大
@media (min-width: 48em) {

    .steedos {
        .slds-form-element_horizontal .slds-form-element__control, .slds-form_horizontal .slds-form-element .slds-form-element__control, .slds-form_stacked .slds-form-element_horizontal .slds-form-element__control {
            padding-left: 18%;
            clear: none;
        }
        .slds-form-element_horizontal .slds-form-element__label, .slds-form_horizontal .slds-form-element .slds-form-element__label, .slds-form_stacked .slds-form-element_horizontal .slds-form-element__label {
            max-width: calc(18% - 1.25rem);
            -ms-flex-preferred-size: calc(18% - 1.25rem);
            flex-basis: calc(18% - 1.25rem);
        }
        .slds-form-element_horizontal.slds-form-element_1-col .slds-form-element__control, .slds-form_horizontal .slds-form-element.slds-form-element_1-col .slds-form-element__control, .slds-form_stacked .slds-form-element_horizontal.slds-form-element_1-col .slds-form-element__control {
            padding-left: calc((50% - 0.75rem - 0.75rem) * .18)
        }
        .slds-form-element_horizontal.slds-form-element_1-col .slds-form-element__label, .slds-form_horizontal .slds-form-element.slds-form-element_1-col .slds-form-element__label, .slds-form_stacked .slds-form-element_horizontal.slds-form-element_1-col .slds-form-element__label {
            width: calc((50% - 0.75rem - 0.75rem) * .18)
        }
    }
    
}

// @media (hover: none) and (pointer: coarse) {
//     body.steedos {
//         font-size: 0.812rem;
//     }
//     @media screen and (max-width: 767px) {
//         body.steedos {
//             font-size: 1rem;
//         }
//     }
// }

// @media screen and (max-width: 767px) {
//     body.steedos {
//         font-size: 1rem;
//     }
// }

.steedos{
    .slds-split-view__list-item-action{
        // 修正列表组件边距过大
        padding: 0.35rem 1rem 0.35rem 1rem;
    }
}